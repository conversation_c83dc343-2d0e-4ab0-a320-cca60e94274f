import { SelectOption } from 'types/common/Item';

export type FieldType = 'text' | 'select' | 'date' | 'number' | 'hidden' | 'date-range';

export interface SearchFieldBase {
    name: string;
    type: FieldType;
    label?: string;
    wrapClassName?: string;
    show?: boolean;
    value?: string | number;
    placeholder?: string;
}

export interface TextField extends SearchFieldBase {
    type: 'text';
}

export interface SelectField extends SearchFieldBase {
    type: 'select';
    options: {
        multiple: boolean;
        choices: SelectOption[];
    };
    onChange?: (value: SelectOption | SelectOption[] | null, fieldName: string) => void;
}

export interface DateField extends SearchFieldBase {
    type: 'date';
}

export interface NumberField extends SearchFieldBase {
    type: 'number';
}

export interface HiddenField extends SearchFieldBase {
    type: 'hidden';
}

export interface DateRangeField extends SearchFieldBase {
    type: 'date-range';
    options?: {
        placeholderText?: string;
        dateFormat?: string;
        isClearable?: boolean;
        defaultValue?: [Date, Date];
        defaultCalendarValue?: [Date, Date];
        startDate?: Date;
        endDate?: Date;
    };
}

export type SearchField = TextField | SelectField | DateField | NumberField | HiddenField | DateRangeField;
